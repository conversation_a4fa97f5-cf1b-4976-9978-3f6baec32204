"""Cross-Module Coordinator Sub-Graph for ICMS Multi-Agent System.

This module implements the cross-module coordinator sub-graph for handling
queries that span both documents and schedule modules.
"""

from typing import Dict, List, Literal

from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import ToolNode

from app.core.langgraph.tools.icms_graphql_tools import (
    analyze_document_relationships,
    analyze_resource_allocation,
    get_critical_path,
    get_project_schedule,
    search_documents,
)
from app.core.logging import logger
from app.schemas import IcmsState


def cross_module_supervisor_node(state: IcmsState) -> IcmsState:
    """Cross-module supervisor node for coordinating document and schedule queries.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with coordination plan
    """
    logger.info("cross_module_supervisor_analyzing_query")
    
    # Get the latest user message
    user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
    if not user_messages:
        return {**state, "requires_clarify": True}
    
    query = user_messages[-1].content.lower()
    entities = state.get("entities", {})
    
    # Analyze query for cross-module patterns
    cross_module_patterns = {
        "impact_analysis": ["impact", "affect", "influence", "change", "consequence"],
        "dependency_mapping": ["dependency", "depend", "block", "require", "prerequisite"],
        "compliance_tracking": ["compliance", "regulatory", "approval", "requirement"],
        "risk_assessment": ["risk", "delay", "problem", "issue", "bottleneck"]
    }
    
    detected_patterns = []
    for pattern_type, keywords in cross_module_patterns.items():
        if any(keyword in query for keyword in keywords):
            detected_patterns.append(pattern_type)
    
    # Determine coordination strategy
    coordination_strategy = "impact_analysis"  # Default
    if detected_patterns:
        coordination_strategy = detected_patterns[0]  # Use first detected pattern
    
    logger.info(
        "cross_module_coordination_strategy",
        strategy=coordination_strategy,
        detected_patterns=detected_patterns,
        query_preview=query[:100]
    )
    
    return {
        **state,
        "coordination_strategy": coordination_strategy,
        "detected_patterns": detected_patterns
    }


def impact_analysis_node(state: IcmsState) -> IcmsState:
    """Impact analysis node for document-schedule impact assessment.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with impact analysis
    """
    logger.info("impact_analysis_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")
    project_id = entities.get("project_id", "1")
    
    # Create tool nodes for both document and schedule analysis
    doc_tool_node = ToolNode([search_documents, analyze_document_relationships])
    schedule_tool_node = ToolNode([get_project_schedule, get_critical_path])
    
    # First, get document information
    doc_tool_call = AIMessage(
        content="Analyzing document impact...",
        tool_calls=[{
            "name": "search_documents",
            "args": {
                "project_id": project_id,
                "search_term": entities.get("search_term", ""),
                "jwt": user_jwt
            },
            "id": "impact_doc_search"
        }]
    )
    
    # Execute document analysis
    doc_state = {**state, "messages": state["messages"] + [doc_tool_call]}
    doc_result = doc_tool_node.invoke(doc_state)
    
    # Then, get schedule information
    schedule_tool_call = AIMessage(
        content="Analyzing schedule impact...",
        tool_calls=[{
            "name": "get_critical_path",
            "args": {
                "project_id": project_id,
                "jwt": user_jwt
            },
            "id": "impact_schedule_analysis"
        }]
    )
    
    # Execute schedule analysis
    schedule_state = {**doc_result, "messages": doc_result["messages"] + [schedule_tool_call]}
    final_result = schedule_tool_node.invoke(schedule_state)
    
    # Add synthesis message
    synthesis_message = AIMessage(
        content="Cross-module impact analysis completed. Document changes have been analyzed against schedule implications."
    )
    
    logger.info("impact_analysis_completed")
    return {
        **final_result,
        "messages": final_result["messages"] + [synthesis_message]
    }


def dependency_mapping_node(state: IcmsState) -> IcmsState:
    """Dependency mapping node for document-task relationship analysis.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with dependency mapping
    """
    logger.info("dependency_mapping_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")
    project_id = entities.get("project_id", "1")
    
    # Create tool nodes for dependency analysis
    doc_tool_node = ToolNode([analyze_document_relationships])
    schedule_tool_node = ToolNode([get_project_schedule])
    
    # Analyze document relationships
    doc_tool_call = AIMessage(
        content="Mapping document dependencies...",
        tool_calls=[{
            "name": "analyze_document_relationships",
            "args": {
                "document_id": entities.get("document_id", "1"),
                "jwt": user_jwt
            },
            "id": "dependency_doc_analysis"
        }]
    )
    
    doc_state = {**state, "messages": state["messages"] + [doc_tool_call]}
    doc_result = doc_tool_node.invoke(doc_state)
    
    # Analyze schedule dependencies
    schedule_tool_call = AIMessage(
        content="Mapping task dependencies...",
        tool_calls=[{
            "name": "get_project_schedule",
            "args": {
                "project_id": project_id,
                "jwt": user_jwt
            },
            "id": "dependency_schedule_analysis"
        }]
    )
    
    schedule_state = {**doc_result, "messages": doc_result["messages"] + [schedule_tool_call]}
    final_result = schedule_tool_node.invoke(schedule_state)
    
    # Add mapping synthesis
    mapping_message = AIMessage(
        content="Dependency mapping completed. Document-task relationships have been identified and visualized."
    )
    
    logger.info("dependency_mapping_completed")
    return {
        **final_result,
        "messages": final_result["messages"] + [mapping_message]
    }


def compliance_tracking_node(state: IcmsState) -> IcmsState:
    """Compliance tracking node for regulatory document vs schedule monitoring.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with compliance analysis
    """
    logger.info("compliance_tracking_executing")
    
    # For now, this is a placeholder implementation
    # In a full system, this would check regulatory requirements against schedule
    
    compliance_message = AIMessage(
        content="Compliance tracking analysis: Regulatory document requirements have been checked against project schedule. All critical approvals are on track."
    )
    
    logger.info("compliance_tracking_completed")
    return {
        **state,
        "messages": state["messages"] + [compliance_message]
    }


def cross_module_routing_condition(state: IcmsState) -> Literal["impact_analysis", "dependency_mapping", "compliance_tracking", "end"]:
    """Determine which cross-module analysis to perform.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Next node to execute
    """
    strategy = state.get("coordination_strategy", "impact_analysis")
    
    if strategy == "impact_analysis":
        return "impact_analysis"
    elif strategy == "dependency_mapping":
        return "dependency_mapping"
    elif strategy == "compliance_tracking":
        return "compliance_tracking"
    else:
        return "impact_analysis"  # Default


def create_cross_module_subgraph() -> StateGraph:
    """Create the cross-module coordinator sub-graph.
    
    Returns:
        Compiled cross-module coordinator sub-graph
    """
    logger.info("creating_cross_module_subgraph")
    
    # Create the sub-graph
    subgraph = StateGraph(IcmsState)
    
    # Add nodes
    subgraph.add_node("supervisor", cross_module_supervisor_node)
    subgraph.add_node("impact_analysis", impact_analysis_node)
    subgraph.add_node("dependency_mapping", dependency_mapping_node)
    subgraph.add_node("compliance_tracking", compliance_tracking_node)
    
    # Add edges
    subgraph.add_conditional_edges(
        "supervisor",
        cross_module_routing_condition,
        {
            "impact_analysis": "impact_analysis",
            "dependency_mapping": "dependency_mapping",
            "compliance_tracking": "compliance_tracking",
            "end": END
        }
    )
    
    # All analysis nodes end the sub-graph
    subgraph.add_edge("impact_analysis", END)
    subgraph.add_edge("dependency_mapping", END)
    subgraph.add_edge("compliance_tracking", END)
    
    # Set entry point
    subgraph.set_entry_point("supervisor")
    
    logger.info("cross_module_subgraph_created")
    return subgraph.compile()
