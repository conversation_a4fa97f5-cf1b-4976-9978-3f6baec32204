"""Document Team Sub-Graph for ICMS Multi-Agent System.

This module implements the document team sub-graph with specialized agents
for search, metadata extraction, and analysis using ToolNode pattern.
"""

from typing import Dict, List, Literal

from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import E<PERSON>, StateGraph
from langgraph.prebuilt import ToolNode

from app.core.langgraph.tools.icms_graphql_tools import (
    analyze_document_relationships,
    search_documents,
)
from app.core.logging import logger
from app.schemas import IcmsState


def document_supervisor_node(state: IcmsState) -> IcmsState:
    """Document team supervisor node for routing document-related queries.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with routing decision
    """
    logger.info("document_supervisor_analyzing_query")
    
    # Get the latest user message
    user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
    if not user_messages:
        return {**state, "requires_clarify": True}
    
    query = user_messages[-1].content.lower()
    entities = state.get("entities", {})
    
    # Determine which document tools to use based on query analysis
    tools_to_use = []
    
    # Check for search-related keywords
    search_keywords = ["find", "search", "show", "list", "get", "documents", "files"]
    if any(keyword in query for keyword in search_keywords):
        tools_to_use.append("search_documents")
    
    # Check for relationship/analysis keywords
    analysis_keywords = ["relationship", "related", "dependency", "impact", "affect", "connect"]
    if any(keyword in query for keyword in analysis_keywords):
        tools_to_use.append("analyze_document_relationships")
    
    # Default to search if no specific tools identified
    if not tools_to_use:
        tools_to_use = ["search_documents"]
    
    logger.info(
        "document_supervisor_routing",
        tools_selected=tools_to_use,
        query_preview=query[:100]
    )
    
    # Store routing decision in state
    return {
        **state,
        "document_tools_to_use": tools_to_use
    }


def document_search_node(state: IcmsState) -> IcmsState:
    """Document search specialist node.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with search results
    """
    logger.info("document_search_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")
    
    # Extract project ID from entities or use default
    project_id = entities.get("project_id", "1")  # Default project for demo
    search_term = entities.get("search_term", "")
    
    # Create a tool node for document search
    search_tool_node = ToolNode([search_documents])
    
    # Prepare tool call message
    tool_call_message = AIMessage(
        content="Searching for documents...",
        tool_calls=[{
            "name": "search_documents",
            "args": {
                "project_id": project_id,
                "search_term": search_term,
                "jwt": user_jwt
            },
            "id": "search_docs_call"
        }]
    )
    
    # Execute the tool
    tool_state = {**state, "messages": state["messages"] + [tool_call_message]}
    result_state = search_tool_node.invoke(tool_state)
    
    logger.info("document_search_completed")
    return result_state


def document_analysis_node(state: IcmsState) -> IcmsState:
    """Document analysis specialist node.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with analysis results
    """
    logger.info("document_analysis_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")
    
    # Extract document ID from entities
    document_id = entities.get("document_id", "1")  # Default for demo
    
    # Create a tool node for document analysis
    analysis_tool_node = ToolNode([analyze_document_relationships])
    
    # Prepare tool call message
    tool_call_message = AIMessage(
        content="Analyzing document relationships...",
        tool_calls=[{
            "name": "analyze_document_relationships",
            "args": {
                "document_id": document_id,
                "jwt": user_jwt
            },
            "id": "analyze_docs_call"
        }]
    )
    
    # Execute the tool
    tool_state = {**state, "messages": state["messages"] + [tool_call_message]}
    result_state = analysis_tool_node.invoke(tool_state)
    
    logger.info("document_analysis_completed")
    return result_state


def document_metadata_node(state: IcmsState) -> IcmsState:
    """Document metadata extraction specialist node.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with metadata extraction results
    """
    logger.info("document_metadata_extracting")
    
    # For now, this is a placeholder that adds metadata context
    # In a full implementation, this would extract metadata from documents
    
    metadata_message = AIMessage(
        content="Document metadata extracted: file types, sizes, creation dates, and approval status analyzed."
    )
    
    return {
        **state,
        "messages": state["messages"] + [metadata_message]
    }


def document_routing_condition(state: IcmsState) -> Literal["search", "analysis", "metadata", "end"]:
    """Determine which document specialist to route to.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Next node to execute
    """
    tools_to_use = state.get("document_tools_to_use", [])
    
    if "search_documents" in tools_to_use:
        return "search"
    elif "analyze_document_relationships" in tools_to_use:
        return "analysis"
    else:
        return "metadata"


def create_document_team_subgraph() -> StateGraph:
    """Create the document team sub-graph.
    
    Returns:
        Compiled document team sub-graph
    """
    logger.info("creating_document_team_subgraph")
    
    # Create the sub-graph
    subgraph = StateGraph(IcmsState)
    
    # Add nodes
    subgraph.add_node("supervisor", document_supervisor_node)
    subgraph.add_node("search", document_search_node)
    subgraph.add_node("analysis", document_analysis_node)
    subgraph.add_node("metadata", document_metadata_node)
    
    # Add edges
    subgraph.add_conditional_edges(
        "supervisor",
        document_routing_condition,
        {
            "search": "search",
            "analysis": "analysis", 
            "metadata": "metadata",
            "end": END
        }
    )
    
    # All specialist nodes end the sub-graph
    subgraph.add_edge("search", END)
    subgraph.add_edge("analysis", END)
    subgraph.add_edge("metadata", END)
    
    # Set entry point
    subgraph.set_entry_point("supervisor")
    
    logger.info("document_team_subgraph_created")
    return subgraph.compile()
