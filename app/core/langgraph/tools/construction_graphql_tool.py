"""Construction project GraphQL tool for LangGraph.

Simple tool to query construction schedule data for active tasks this month.
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Type

from langchain_core.callbacks import AsyncCallbackManagerForToolRun
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from app.core.logging import logger
from .graphql_client import ConstructionScheduleQueryBuilder, GraphQLClient


class ConstructionGraphQLInput(BaseModel):
    """Input schema for construction GraphQL queries."""
    
    query: str = Field(description="Natural language query about construction schedules")
    project_id: Optional[str] = Field(default=None, description="Project ID to filter results")
    limit: Optional[int] = Field(default=50, description="Maximum number of results")


class ConstructionGraphQLTool(BaseTool):
    """Tool for querying construction project data via GraphQL."""
    
    name: str = "construction_schedule_query"
    description: str = "Query construction schedule data for active tasks this month"
    args_schema: Type[BaseModel] = ConstructionGraphQLInput
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._client: Optional[GraphQLClient] = None
        self._query_builder: Optional[ConstructionScheduleQueryBuilder] = None
    
    @property
    def client(self) -> GraphQLClient:
        if self._client is None:
            from app.core.config import settings
            endpoint = getattr(settings, 'GRAPHQL_ENDPOINT', 'http://localhost:3000/graphql')
            self._client = GraphQLClient(endpoint)
        return self._client
    
    @property
    def query_builder(self) -> ConstructionScheduleQueryBuilder:
        if self._query_builder is None:
            self._query_builder = ConstructionScheduleQueryBuilder()
        return self._query_builder
    
    def _parse_query(self, query: str, project_id: Optional[str] = None) -> Dict[str, Any]:
        """Parse query to determine filters in GraphQL format."""
        query_lower = query.lower()
        filter_obj = {}
        
        # Add project filter
        if project_id:
            filter_obj['projectScheduleId'] = {'eq': int(project_id)}
        
        # Check for "active" tasks
        # if 'active' in query_lower or 'tasks that active' in query_lower:
        #     filter_obj['status'] = {'eq': 'InProgress'}
        
        # Check for "this month"
        if 'this month' in query_lower:
            now = datetime.now()
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            filter_obj['baselineStart'] = {
                'gt': start_of_month.isoformat() + 'Z'
            }
        
        return {'filter': filter_obj}
    
    def _format_results(self, results: Dict[str, Any]) ->a str:
        """Format GraphQL results."""
        if 'data' not in results or not results['data'].get('schedules'):
            return "No schedules found."
        
        schedules = results['data']['schedules']
        nodes = schedules.get('nodes', [])
        
        if not nodes:
            return "No tasks found matching the criteria."
        
        # Format response
        response = f"Found {len(nodes)} schedule(s):\n\n"
        
        for schedule in nodes:
            response += f"**{schedule['name']}** (ID: {schedule['id']})\n"
            response += f"  WBS: {schedule.get('wbs', 'N/A')}\n"
            response += f"  Status: {schedule.get('status', 'N/A')}\n"
            response += f"  Progress: {schedule.get('percentComplete', 0)}%\n"
            
            if schedule.get('baselineStart'):
                response += f"  Baseline Start: {schedule['baselineStart']}\n"
            if schedule.get('baselineFinish'):
                response += f"  Baseline Finish: {schedule['baselineFinish']}\n"
            
            response += "\n"
        
        # Truncate if too long
        max_length = 2800
        if len(response) > max_length:
            response = response[:max_length] + "\n\n... (response truncated)"
        
        return response
    
    def _run(self, query: str, project_id: Optional[str] = None, limit: Optional[int] = 50) -> str:
        raise NotImplementedError("This tool only supports async execution")
    
    async def _arun(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: Optional[int] = 50,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
        auth_token: Optional[str] = None,
    ) -> str:
        """Execute the GraphQL query."""
        try:
            # Get auth token from metadata if available
            metadata_auth_token = None
            if run_manager and hasattr(run_manager, 'metadata') and run_manager.metadata:
                metadata_auth_token = run_manager.metadata.get('auth_token')
            
            final_auth_token = auth_token or metadata_auth_token
            
            # Parse query to get filters in GraphQL format
            variables = self._parse_query(query, project_id)
            
            # Add paging
            if limit:
                variables['paging'] = {'limit': limit}
            
            # Build GraphQL query
            graphql_query = self.query_builder.build_schedules_query()
            
            # Log what we're executing
            logger.info(
                "graphql_query_executing",
                query=query,
                graphql_query=graphql_query,
                variables=variables,
                auth_token_present=bool(final_auth_token)
            )
            
            # Execute query
            results = await self.client.execute_query(
                query=graphql_query,
                variables=variables,
                auth_token=final_auth_token
            )
            
            # Format and return results
            formatted_results = self._format_results(results)
            
            logger.info(
                "graphql_query_completed",
                query=query,
                results_count=len(results.get('data', {}).get('schedules', {}).get('nodes', [])),
                response_length=len(formatted_results)
            )
            
            return formatted_results
            
        except Exception as e:
            error_msg = f"Error executing GraphQL query: {str(e)}"
            logger.error("graphql_query_error", error=str(e), query=query)
            return error_msg
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.close()


# Create the tool instance
construction_schedule_tool = ConstructionGraphQLTool()