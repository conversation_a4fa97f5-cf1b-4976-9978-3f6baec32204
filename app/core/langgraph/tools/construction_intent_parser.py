"""
construction_intent_parser.py
Full, production-ready LLM parser for construction-schedule queries.
Requires: pip install openai pydantic jsonschema
"""

import json
import os
from datetime import datetime, timedelta
from typing import Any, Dict, Tuple

import openai
from jsonschema import validate, ValidationError

# --------------------------------------------------
# JSON Schema the LLM must adhere to
# --------------------------------------------------
INTENT_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": {
        "projectId":        {"type": ["string", "null"]},
        "status":           {
            "type": ["array", "null"],
            "items": {"enum": ["InProgress", "NotStarted", "Completed"]}
        },
        "dateRange": {
            "type": ["object", "null"],
            "properties": {
                "granularity": {"enum": ["this_month", "last_30_days", "custom"]},
                "start":       {"type": ["string", "null"], "format": "date-time"},
                "end":         {"type": ["string", "null"], "format": "date-time"}
            },
            "required": ["granularity"]
        },
        "limit": {"type": ["integer", "null"], "minimum": 1, "maximum": 1000}
    },
    "additionalProperties": False
}

# --------------------------------------------------
# Few-shot prompt template
# --------------------------------------------------
SYSTEM_PROMPT = f"""
You are an assistant that converts natural-language questions into a JSON intent object for a construction GraphQL API.

The JSON **must** match the following schema:
{json.dumps(INTENT_SCHEMA, indent=2)}

Allowed status values: ["InProgress", "NotStarted", "Completed"]
Date keywords:
- "this month"     → {{"granularity": "this_month"}}
- "last 30 days"   → {{"granularity": "last_30_days"}}
- "since 2024-07-01 until 2024-07-14" → {{"granularity": "custom", "start": "2024-07-01T00:00:00Z", "end": "2024-07-14T23:59:59Z"}}

Examples:
User: "Show me active tasks for project 42 this month"
JSON: {{"projectId":"42","status":["InProgress"],"dateRange":{{"granularity":"this_month"}},"limit":50}}

User: "All overdue schedules"
JSON: {{"projectId":null,"status":["NotStarted"],"dateRange":{{"granularity":"last_30_days"}},"limit":100}}

Return **only** valid JSON. Do not include explanations or markdown fences.
"""

# --------------------------------------------------
# Parser class
# --------------------------------------------------
class ConstructionIntentParser:
    def __init__(self, model: str = "gpt-4-turbo-preview") -> None:
        self.model = model
        self.client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    async def parse(self, query: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Parse user query into a validated JSON intent.
        Returns (success: bool, intent_dict: dict)
        """
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": query}
        ]

        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.0,
                max_tokens=400
            )
            raw = response.choices[0].message.content.strip()
            intent = json.loads(raw)
            validate(instance=intent, schema=INTENT_SCHEMA)
            return True, intent
        except (json.JSONDecodeError, ValidationError, openai.APIError) as e:
            return False, {"error": str(e)}

# --------------------------------------------------
# JSON → GraphQL variables translator
# --------------------------------------------------
def json_to_graphql_vars(intent: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert the validated JSON intent into GraphQL query variables.
    """
    variables: Dict[str, Any] = {"filter": {}, "paging": {}}

    # Project filter
    if intent.get("projectId"):
        variables["filter"]["projectScheduleId"] = {"eq": int(intent["projectId"])}

    # Status filter
    status = intent.get("status")
    if status:
        variables["filter"]["status"] = {"in": status} if len(status) > 1 else {"eq": status[0]}

    # Date range
    dr = intent.get("dateRange")
    if dr:
        now = datetime.utcnow()
        if dr["granularity"] == "this_month":
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end = (start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)
            variables["filter"]["baselineStart"] = {"gte": start.isoformat() + "Z", "lte": end.isoformat() + "Z"}
        elif dr["granularity"] == "last_30_days":
            start = now - timedelta(days=30)
            variables["filter"]["baselineStart"] = {"gte": start.isoformat() + "Z"}
        elif dr["granularity"] == "custom":
            if dr.get("start"):
                variables["filter"]["baselineStart"] = {"gte": dr["start"]}
            if dr.get("end"):
                variables["filter"]["baselineFinish"] = {"lte": dr["end"]}

    # Paging
    limit = intent.get("limit")
    if limit:
        variables["paging"]["limit"] = limit

    return variables