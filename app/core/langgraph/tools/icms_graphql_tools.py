"""ICMS GraphQL tools for document and schedule operations.

This module implements GraphQL tools for the ICMS multi-agent system using
the tool decorator pattern as specified in the development blueprint.
"""

import asyncio
from typing import Any, Dict, Optional

import httpx
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.logging import logger


class GraphQLError(Exception):
    """Exception raised for GraphQL errors."""
    pass


async def execute_graphql_query(
    query: str,
    variables: Optional[Dict[str, Any]] = None,
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """Execute a GraphQL query against the ICMS backend.
    
    Args:
        query: GraphQL query string
        variables: Query variables
        auth_token: JWT token for authentication
        
    Returns:
        GraphQL response data
        
    Raises:
        GraphQLError: If the query fails or returns errors
    """
    endpoint = getattr(settings, 'GRAPHQL_ENDPOINT', 'http://localhost:3000/graphql')
    headers = {"Content-Type": "application/json"}
    
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    
    payload = {"query": query}
    if variables:
        payload["variables"] = variables
    
    logger.info(
        "graphql_query_executing",
        endpoint=endpoint,
        has_auth=bool(auth_token),
        variables_count=len(variables) if variables else 0
    )
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(endpoint, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            if "errors" in result:
                error_messages = [error.get("message", str(error)) for error in result["errors"]]
                logger.error("graphql_query_errors", errors=error_messages)
                raise GraphQLError(f"GraphQL errors: {', '.join(error_messages)}")
            
            logger.info("graphql_query_success", data_keys=list(result.get("data", {}).keys()))
            return result.get("data", {})
            
    except httpx.HTTPError as e:
        logger.error("graphql_http_error", error=str(e), endpoint=endpoint)
        raise GraphQLError(f"HTTP error: {e}")
    except Exception as e:
        logger.error("graphql_unexpected_error", error=str(e))
        raise GraphQLError(f"Unexpected error: {e}")


@tool
async def search_documents(project_id: str, search_term: str = "", jwt: str = "") -> dict:
    """Search for documents in the ICMS system.
    
    Args:
        project_id: ID of the project to search documents in
        search_term: Search term to filter documents
        jwt: JWT token for authentication
        
    Returns:
        Dictionary containing document search results
    """
    query = """
    query SearchDocuments($projectId: ID!, $searchTerm: String) {
        documents(projectId: $projectId, searchTerm: $searchTerm) {
            id
            title
            type
            status
            version
            createdAt
            updatedAt
            approvalStatus
            metadata {
                size
                format
                tags
            }
            project {
                id
                name
            }
        }
    }
    """
    
    variables = {
        "projectId": project_id,
        "searchTerm": search_term
    }
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"documents": result.get("documents", [])}
    except GraphQLError as e:
        logger.error("document_search_failed", error=str(e))
        return {"error": str(e), "documents": []}


@tool
async def get_project_schedule(project_id: str, jwt: str = "") -> dict:
    """Get project schedule information from ICMS.
    
    Args:
        project_id: ID of the project to get schedule for
        jwt: JWT token for authentication
        
    Returns:
        Dictionary containing project schedule data
    """
    query = """
    query GetProjectSchedule($projectId: ID!) {
        projectSchedule(id: $projectId) {
            id
            name
            startDate
            endDate
            status
            tasks {
                id
                name
                status
                baselineStart
                baselineFinish
                actualStart
                actualFinish
                duration
                progress
                assignees {
                    id
                    name
                    role
                }
            }
            milestones {
                id
                name
                date
                status
                description
            }
        }
    }
    """
    
    variables = {"projectId": project_id}
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"schedule": result.get("projectSchedule")}
    except GraphQLError as e:
        logger.error("schedule_fetch_failed", error=str(e))
        return {"error": str(e), "schedule": None}


@tool
async def get_critical_path(project_id: str, jwt: str = "") -> dict:
    """Get critical path analysis for a project.
    
    Args:
        project_id: ID of the project to analyze
        jwt: JWT token for authentication
        
    Returns:
        Dictionary containing critical path analysis
    """
    query = """
    query GetCriticalPath($projectId: ID!) {
        criticalPath(projectId: $projectId) {
            tasks {
                id
                name
                baselineStart
                baselineFinish
                duration
                float
                isCritical
            }
            totalDuration
            criticalPathLength
        }
    }
    """
    
    variables = {"projectId": project_id}
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"criticalPath": result.get("criticalPath")}
    except GraphQLError as e:
        logger.error("critical_path_analysis_failed", error=str(e))
        return {"error": str(e), "criticalPath": None}


@tool
async def analyze_document_relationships(document_id: str, jwt: str = "") -> dict:
    """Analyze relationships and dependencies for a document.
    
    Args:
        document_id: ID of the document to analyze
        jwt: JWT token for authentication
        
    Returns:
        Dictionary containing document relationship analysis
    """
    query = """
    query AnalyzeDocumentRelationships($documentId: ID!) {
        documentRelationships(documentId: $documentId) {
            document {
                id
                title
                type
            }
            relatedDocuments {
                id
                title
                type
                relationshipType
            }
            affectedTasks {
                id
                name
                status
                impact
            }
            dependencies {
                type
                description
                status
            }
        }
    }
    """
    
    variables = {"documentId": document_id}
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"relationships": result.get("documentRelationships")}
    except GraphQLError as e:
        logger.error("document_relationship_analysis_failed", error=str(e))
        return {"error": str(e), "relationships": None}


@tool
async def analyze_resource_allocation(project_id: str, start_date: str = "", end_date: str = "", jwt: str = "") -> dict:
    """Analyze resource allocation for a project within a date range.
    
    Args:
        project_id: ID of the project to analyze
        start_date: Start date for analysis (ISO format)
        end_date: End date for analysis (ISO format)
        jwt: JWT token for authentication
        
    Returns:
        Dictionary containing resource allocation analysis
    """
    query = """
    query AnalyzeResourceAllocation($projectId: ID!, $dateRange: DateRange) {
        resourceAllocation(projectId: $projectId, dateRange: $dateRange) {
            resources {
                id
                name
                type
                capacity
                allocation {
                    taskId
                    taskName
                    allocatedHours
                    startDate
                    endDate
                }
            }
            conflicts {
                resourceId
                resourceName
                conflictingTasks {
                    id
                    name
                    startDate
                    endDate
                }
            }
        }
    }
    """
    
    variables = {
        "projectId": project_id,
        "dateRange": {
            "start": start_date,
            "end": end_date
        } if start_date and end_date else None
    }
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"resourceAllocation": result.get("resourceAllocation")}
    except GraphQLError as e:
        logger.error("resource_allocation_analysis_failed", error=str(e))
        return {"error": str(e), "resourceAllocation": None}
