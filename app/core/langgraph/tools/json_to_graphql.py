"""
json_to_graphql.py
Deterministic translator: JSON intent  ->  GraphQL variables + query string
"""
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# --------------------------------------------------
# 1. GraphQL query builder (re-usable fragments)
# --------------------------------------------------
SCHEDULES_QUERY = """
query GetSchedules($filter: ScheduleFilter, $paging: PageInput) {
  schedules(filter: $filter, paging: $paging) {
    nodes {
      id
      name
      wbs
      status
      percentComplete
      baselineStart
      baselineFinish
    }
    totalCount
  }
}
"""

# --------------------------------------------------
# 2. JSON  ->  GraphQL variables
# --------------------------------------------------
def json_to_graphql_variables(intent: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert the LLM-produced JSON intent into GraphQL variables
    compatible with the Schedules query above.
    """
    variables: Dict[str, Any] = {"filter": {}, "paging": {}}

    # 2.1  Project filter
    if intent.get("projectId"):
        variables["filter"]["projectScheduleId"] = {"eq": int(intent["projectId"])}

    # 2.2  Status filter
    status = intent.get("status")
    if status:
        if len(status) == 1:
            variables["filter"]["status"] = {"eq": status[0]}
        else:
            variables["filter"]["status"] = {"in": status}

    # 2.3  Date range
    dr = intent.get("dateRange")
    if dr:
        now = datetime.utcnow()
        if dr["granularity"] == "this_month":
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end = (start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)
            variables["filter"]["baselineStart"] = {
                "gte": start.isoformat() + "Z",
                "lte": end.isoformat()   + "Z"
            }
        elif dr["granularity"] == "last_30_days":
            start = now - timedelta(days=30)
            variables["filter"]["baselineStart"] = {"gte": start.isoformat() + "Z"}
        elif dr["granularity"] == "custom":
            if dr.get("start"):
                variables["filter"]["baselineStart"] = {"gte": dr["start"]}
            if dr.get("end"):
                variables["filter"]["baselineFinish"] = {"lte": dr["end"]}

    # 2.4  Limit / paging
    limit = intent.get("limit")
    if limit:
        variables["paging"]["limit"] = limit

    # Remove empty dicts to keep GraphQL clean
    variables = {k: v for k, v in variables.items() if v}
    if "filter" in variables and not variables["filter"]:
        del variables["filter"]
    if "paging" in variables and not variables["paging"]:
        del variables["paging"]

    return variables

# --------------------------------------------------
# 3. Convenience wrapper
# --------------------------------------------------
def build_graphql_payload(intent: Dict[str, Any]) -> Dict[str, Any]:
    """
    Returns a ready-to-use payload for an HTTP POST to /graphql
    """
    return {
        "query": SCHEDULES_QUERY,
        "variables": json_to_graphql_variables(intent)
    }